[{"path": "/s3_nfs/data/concatenated_80BT_fa_en/test/", "text_column": "text", "split": "test", "config": null}, {"path": "/s3_nfs/data/Glotcc/test/", "text_column": "content", "split": "test", "config": null}, {"path": "/s3_nfs/data/persian_blog_V2/test/", "text_column": "text", "split": "test", "config": null}, {"path": "/s3_nfs/data/persian_news_dataset_corrected/test/", "text_column": "text", "split": "test", "config": null}, {"path": "/s3_nfs/data/pile_wikipedia/test/", "text_column": "text", "split": "test", "config": null}, {"path": "/s3_nfs/data/shahed_dataset/test/", "text_column": "content", "split": "test", "config": null}, {"path": "/s3_nfs/data/isna_news/test/", "text_column": "TEXT", "split": "test", "config": null}, {"path": "/s3_nfs/data/Wikipedia_omarkamali_splitted/test_split/", "text_column": "text", "split": "test", "config": null}, {"path": "/s3_nfs/data/pile_openwebtext/test/", "text_column": "text", "split": "test", "config": null}]