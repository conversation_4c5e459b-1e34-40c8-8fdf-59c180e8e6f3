#!/usr/bin/env python3
"""
Dataset Token Analysis Script

This script analyzes token counts and statistics for Hugging Face datasets.
It can work with both local datasets and datasets loaded from disk.
"""

import argparse
import logging
import os
from collections import defaultdict
from typing import Optional

from datasets import Dataset, load_from_disk
from transformers import AutoTokenizer
from tqdm import tqdm

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def analyze_tokens(dataset: Dataset, tokenizer_name: str, output_path: str, num_workers: int = 4, split_name: str = "dataset") -> None:
    """Analyze token counts per source and create a markdown report."""
    logger.info(f"Starting token analysis with tokenizer: {tokenizer_name}")
    
    # Load tokenizer
    try:
        tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)
        logger.info(f"Loaded tokenizer: {tokenizer_name}")
    except Exception as e:
        logger.error(f"Failed to load tokenizer {tokenizer_name}: {e}")
        return
    
    # Create a function that can be used with multiprocessing
    def add_token_stats(example):
        """Add token statistics to each example."""
        text = example['text']
        source = example['source']
        
        if text:  # Skip empty texts
            tokens = tokenizer.encode(text, add_special_tokens=False)
            return {
                'text': text,
                'source': source,
                'token_count': len(tokens),
                'char_count': len(text)
            }
        else:
            return {
                'text': text,
                'source': source,
                'token_count': 0,
                'char_count': 0
            }
    
    # Validate dataset structure
    if len(dataset) == 0:
        logger.warning("Dataset is empty, skipping token analysis")
        return
    
    # Check if required columns exist
    required_columns = ['text', 'source']
    missing_columns = [col for col in required_columns if col not in dataset.column_names]
    if missing_columns:
        logger.error(f"Missing required columns: {missing_columns}")
        return
    
    logger.info(f"Dataset has {len(dataset):,} examples with columns: {dataset.column_names}")
    
    # Process dataset to add token statistics
    logger.info("Counting tokens...")
    try:
        dataset_with_stats = dataset.map(
            add_token_stats,
            desc="Counting tokens",
            num_proc=min(num_workers, os.cpu_count() or 1)
        )
    except Exception as e:
        logger.error(f"Error during token counting: {e}")
        logger.info("Retrying with single process...")
        dataset_with_stats = dataset.map(
            add_token_stats,
            desc="Counting tokens (single process)"
        )
    
    # Aggregate statistics per source
    source_stats = defaultdict(lambda: {'examples': 0, 'tokens': 0, 'chars': 0})
    
    logger.info("Aggregating token statistics...")
    for i, example in enumerate(dataset_with_stats):
        if i % 100000 == 0 and i > 0:
            logger.info(f"Processed {i:,} examples for aggregation")
        
        source = example['source']
        source_stats[source]['examples'] += 1
        source_stats[source]['tokens'] += example['token_count']
        source_stats[source]['chars'] += example['char_count']
    
    logger.info(f"Aggregation complete. Found {len(source_stats)} sources.")
    
    # Calculate totals
    total_examples = sum(stats['examples'] for stats in source_stats.values())
    total_tokens = sum(stats['tokens'] for stats in source_stats.values())
    total_chars = sum(stats['chars'] for stats in source_stats.values())
    
    # Create markdown report
    report_path = os.path.join(output_path, f"token_analysis_report_{split_name}.md")
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(f"# Dataset Token Analysis Report - {split_name.title()} Split\n\n")
        f.write(f"**Tokenizer Used:** `{tokenizer_name}`\n\n")
        f.write(f"**Split:** {split_name}\n\n")
        f.write(f"**Total Examples:** {total_examples:,}\n")
        f.write(f"**Total Tokens:** {total_tokens:,}\n")
        f.write(f"**Total Characters:** {total_chars:,}\n")
        if total_examples > 0:
            f.write(f"**Average Tokens per Example:** {total_tokens/total_examples:.2f}\n\n")
        else:
            f.write("**Average Tokens per Example:** 0.00\n\n")
        
        f.write("## Per-Source Statistics\n\n")
        f.write("| Source | Examples | Tokens | Characters | Avg Tokens/Example | % of Total Tokens |\n")
        f.write("|--------|----------|--------|------------|-------------------|------------------|\n")
        
        # Sort by token count (descending)
        sorted_sources = sorted(source_stats.items(), key=lambda x: x[1]['tokens'], reverse=True)
        
        for source, stats in sorted_sources:
            examples = stats['examples']
            tokens = stats['tokens']
            chars = stats['chars']
            avg_tokens = tokens / examples if examples > 0 else 0
            token_percentage = (tokens / total_tokens) * 100 if total_tokens > 0 else 0
            
            f.write(f"| {source} | {examples:,} | {tokens:,} | {chars:,} | {avg_tokens:.2f} | {token_percentage:.2f}% |\n")
        
        f.write("\n## Summary\n\n")
        f.write(f"- **Number of Sources:** {len(source_stats)}\n")
        if sorted_sources:
            f.write(f"- **Largest Source:** {sorted_sources[0][0]} ({sorted_sources[0][1]['tokens']:,} tokens)\n")
            f.write(f"- **Smallest Source:** {sorted_sources[-1][0]} ({sorted_sources[-1][1]['tokens']:,} tokens)\n")
        else:
            f.write("- **No sources found**\n")
        
        # Token distribution analysis
        f.write("\n## Token Distribution\n\n")
        if total_tokens > 0:
            for source, stats in sorted_sources:
                percentage = (stats['tokens'] / total_tokens) * 100
                bar_length = int(percentage / 2)  # Scale for visualization
                bar = '█' * bar_length
                f.write(f"**{source}:** {percentage:.1f}% {bar}\n")
        else:
            f.write("No tokens found in dataset.\n")
    
    logger.info(f"Token analysis report saved to: {report_path}")


def load_dataset_from_path(dataset_path: str, split_name: Optional[str] = None) -> Dataset:
    """Load a dataset from a local path."""
    logger.info(f"Loading dataset from: {dataset_path}")
    
    if os.path.isdir(dataset_path):
        # Load from disk (HuggingFace format)
        dataset = load_from_disk(dataset_path)
        logger.info(f"Loaded dataset with {len(dataset):,} examples")
        return dataset
    else:
        raise ValueError(f"Dataset path {dataset_path} is not a valid directory")


def main():
    parser = argparse.ArgumentParser(
        description="Analyze token counts and statistics for Hugging Face datasets",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Analyze a single dataset split
  python analyze_dataset_tokens.py --dataset_path ./concatenated_datasets/train --tokenizer_name bert-base-multilingual-cased --split_name train
  
  # Analyze with custom output directory
  python analyze_dataset_tokens.py --dataset_path ./data/test --tokenizer_name bert-base-uncased --output_path ./reports --split_name test
  
  # Use debug mode with more workers
  python analyze_dataset_tokens.py --dataset_path ./data/train --tokenizer_name distilbert-base-uncased --num_workers 8 --debug
"""
    )
    
    parser.add_argument("--dataset_path", required=True, help="Path to the dataset directory")
    parser.add_argument("--tokenizer_name", required=True, help="Tokenizer name or path for analysis")
    parser.add_argument("--output_path", default=".", help="Output directory for reports (default: current directory)")
    parser.add_argument("--split_name", default="dataset", help="Name of the split being analyzed (for report naming)")
    parser.add_argument("--num_workers", type=int, default=4, help="Number of workers for parallel processing (default: 4)")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    
    args = parser.parse_args()
    
    # Set debug logging if requested
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("Debug logging enabled")
    
    # Create output directory
    os.makedirs(args.output_path, exist_ok=True)
    
    logger.info(f"Using {args.num_workers} workers for processing")
    
    try:
        # Load dataset
        dataset = load_dataset_from_path(args.dataset_path, args.split_name)
        
        # Perform token analysis
        logger.info(f"Performing token analysis for {args.split_name} split...")
        analyze_tokens(dataset, args.tokenizer_name, args.output_path, args.num_workers, args.split_name)
        
        logger.info("Token analysis completed successfully!")
        
    except Exception as e:
        logger.error(f"Error during analysis: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())