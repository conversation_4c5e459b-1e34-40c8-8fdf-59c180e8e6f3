[{"path": "/s3_nfs/data/concatenated_80BT_fa_en/train/", "text_column": "text", "split": "train", "config": null}, {"path": "/s3_nfs/data/Glotcc/train/", "text_column": "content", "split": "train", "config": null}, {"path": "/s3_nfs/data/persian_blog_V2/train/", "text_column": "text", "split": "train", "config": null}, {"path": "/s3_nfs/data/persian_news_dataset_corrected/train/", "text_column": "text", "split": "train", "config": null}, {"path": "/s3_nfs/data/pile_wikipedia/train/", "text_column": "text", "split": "train", "config": null}, {"path": "/s3_nfs/data/shahed_dataset/train/", "text_column": "content", "split": "train", "config": null}, {"path": "/s3_nfs/data/isna_news/train/", "text_column": "TEXT", "split": "train", "config": null}, {"path": "/s3_nfs/data/Wikipedia_omarkamali_splitted/train_split/", "text_column": "text", "split": "train", "config": null}, {"path": "/s3_nfs/data/pile_openwebtext/train/", "text_column": "text", "split": "train", "config": null}]