#!/usr/bin/env python3
"""
Script to concatenate multiple local HuggingFace datasets into a single dataset.
Supports shuffling during concatenation and token analysis.

Usage:
    python concatenate_multiple_local_hf_ds_to_one_hf_ds.py \
        --datasets_info_path /path/to/datasets_info.json \
        --output_path /path/to/output \
        --tokenizer_name bert-base-uncased \
        --shuffle
"""

import argparse
import json
import os
from pathlib import Path
from typing import Dict, List, Optional
import logging

try:
    from datasets import Dataset, load_from_disk, concatenate_datasets
except ImportError as e:
    print(f"Error importing required libraries: {e}")
    print("Please install: pip install datasets transformers")
    exit(1)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def load_datasets_info(datasets_info_path: str) -> Dict:
    """Load datasets information from JSON file."""
    with open(datasets_info_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def load_and_process_dataset(dataset_info: Dict, split: str) -> Dataset:
    """Load a single dataset and process it to have consistent columns."""
    path = dataset_info['path']
    text_column = dataset_info['text_column']
    name = dataset_info['name']
    
    logger.info(f"Loading {split} dataset from {path} (source: {name})")
    
    try:
        # Load dataset from disk
        dataset = load_from_disk(path)
        
        # If dataset has splits, get the specified split
        if hasattr(dataset, 'keys') and split in dataset:
            dataset = dataset[split]
        elif hasattr(dataset, 'keys') and len(dataset.keys()) == 1:
            # If only one split exists, use it
            dataset = dataset[list(dataset.keys())[0]]
        
        # Create new dataset with consistent structure
        def process_example(example, idx):
            return {
                'id': f"{name}_{idx}",
                'text': example[text_column],
                'source': name
            }
        
        # Process dataset
        processed_dataset = dataset.map(
            lambda example, idx: process_example(example, idx),
            with_indices=True,
            remove_columns=dataset.column_names,
            desc=f"Processing {name} {split}",
            num_proc=min(4, os.cpu_count() or 1)  # Use up to 4 cores for processing
        )
        
        logger.info(f"Processed {len(processed_dataset)} examples from {name} {split}")
        return processed_dataset
        
    except Exception as e:
        logger.error(f"Error loading dataset from {path}: {e}")
        return None


def concatenate_datasets_with_shuffle(datasets: List[Dataset], shuffle: bool = True) -> Dataset:
    """Concatenate datasets and optionally shuffle."""
    logger.info(f"Concatenating {len(datasets)} datasets...")
    
    # Concatenate all datasets
    concatenated = concatenate_datasets(datasets)
    
    if shuffle:
        logger.info("Shuffling concatenated dataset...")
        concatenated = concatenated.shuffle(seed=42)
    
    return concatenated

def main():
    parser = argparse.ArgumentParser(description="Concatenate multiple local HuggingFace datasets")
    parser.add_argument("--datasets_info_path", required=True, help="Path to datasets info JSON file")
    parser.add_argument("--output_path", required=True, help="Output directory path")
    parser.add_argument("--tokenizer_name", help="Tokenizer name or path for analysis (required only if --no_skip_analysis is used)")
    parser.add_argument("--shuffle", action="store_true", help="Shuffle the concatenated dataset")
    parser.add_argument("--splits", nargs="+", default=["train"], help="Dataset splits to process (default: train)")
    parser.add_argument("--num_workers", type=int, default=4, help="Number of workers for parallel processing (default: 4)")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    parser.add_argument("--no_skip_analysis", action="store_true", help="Enable token analysis (requires --tokenizer_name)")
    
    args = parser.parse_args()
    
    # Set debug logging if requested
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("Debug logging enabled")
    
    # Create output directory
    os.makedirs(args.output_path, exist_ok=True)
    
    logger.info(f"Using {args.num_workers} workers for processing")
    
    # Load datasets info
    datasets_info = load_datasets_info(args.datasets_info_path)
    
    # Process each split
    for split in args.splits:
        if split not in datasets_info:
            logger.warning(f"Split '{split}' not found in datasets info")
            continue
            
        logger.info(f"Processing {split} split...")
        
        # Load and process all datasets for this split
        datasets = []
        for dataset_info in datasets_info[split]:
            dataset = load_and_process_dataset(dataset_info, split)
            if dataset is not None:
                datasets.append(dataset)
        
        if not datasets:
            logger.error(f"No datasets loaded for {split} split")
            continue
        
        # Concatenate datasets
        concatenated_dataset = concatenate_datasets_with_shuffle(datasets, args.shuffle)
        
        # Save concatenated dataset
        split_output_path = os.path.join(args.output_path, split)
        logger.info(f"Saving {split} dataset to {split_output_path}")
        try:
            concatenated_dataset.save_to_disk(split_output_path, num_proc=min(args.num_workers, os.cpu_count() or 1))
            logger.info(f"Saved {len(concatenated_dataset)} examples for {split} split")
        except Exception as e:
            logger.error(f"Error saving dataset: {e}")
            # Fallback to single-threaded save
            logger.info("Retrying with single-threaded save...")
            concatenated_dataset.save_to_disk(split_output_path)
        
            
    logger.info("Dataset concatenation and analysis completed!")


if __name__ == "__main__":
    main()