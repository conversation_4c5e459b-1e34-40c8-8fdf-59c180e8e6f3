# Dataset Concatenation Tool

This tool concatenates multiple local HuggingFace datasets into a single unified dataset with shuffling and token analysis capabilities.

## Features

- **Multi-dataset concatenation**: Combine multiple local HuggingFace datasets
- **Shuffling**: Optional shuffling during concatenation for better data distribution
- **Consistent schema**: Outputs datasets with `id`, `text`, and `source` columns
- **Token analysis**: Comprehensive token counting and analysis per source
- **Markdown reports**: Generates detailed analysis reports
- **Flexible configuration**: JSON-based dataset configuration

## Usage

### Basic Usage

```bash
python concatenate_multiple_local_hf_ds_to_one_hf_ds.py \
    --datasets_info_path /path/to/datasets_info.json \
    --output_path /path/to/output \
    --tokenizer_name bert-base-uncased \
    --shuffle
```

### Arguments

- `--datasets_info_path`: Path to the JSON file containing dataset information
- `--output_path`: Directory where the concatenated dataset will be saved
- `--tokenizer_name`: Tokenizer to use for token analysis (required only with `--no_skip_analysis`)
- `--shuffle`: Shuffle the concatenated dataset (optional)
- `--splits`: Dataset splits to process (default: ["train"])
- `--num_workers`: Number of workers for parallel processing (default: 4)
- `--debug`: Enable debug logging for troubleshooting
- `--no_skip_analysis`: Enable token analysis (requires `--tokenizer_name`). **Note: Token analysis is now handled by a separate script** (useful for testing)

### Example with Persian Corpus

```bash
# Basic usage - concatenate only (default behavior)
python concatenate_multiple_local_hf_ds_to_one_hf_ds.py \
    --datasets_info_path 50BT_only_persian_corpus_ds_info.json \
    --output_path ./concatenated_datasets \
    --shuffle \
    --splits train test

# Show token analysis command (but don't run it)
python concatenate_multiple_local_hf_ds_to_one_hf_ds.py \
    --datasets_info_path 50BT_only_persian_corpus_ds_info.json \
    --output_path ./concatenated_datasets \
    --tokenizer_name bert-base-multilingual-cased \
    --shuffle \
    --splits train test \
    --no_skip_analysis

# Debug mode with more workers
python concatenate_multiple_local_hf_ds_to_one_hf_ds.py \
    --datasets_info_path 50BT_only_persian_corpus_ds_info.json \
    --output_path ./concatenated_datasets \
    --num_workers 8 \
    --debug
```

## Token Analysis (Separate Script)

Token analysis is now handled by a dedicated script for better separation of concerns:

```bash
# Analyze tokens for a specific dataset split
python analyze_dataset_tokens.py \
    --dataset_path ./concatenated_datasets/train \
    --tokenizer_name bert-base-multilingual-cased \
    --split_name train \
    --output_path ./concatenated_datasets

# Analyze with debug logging and more workers
python analyze_dataset_tokens.py \
    --dataset_path ./concatenated_datasets/test \
    --tokenizer_name bert-base-multilingual-cased \
    --split_name test \
    --output_path ./reports \
    --num_workers 8 \
    --debug
```

## Dataset Info JSON Format

The dataset info JSON should have the following structure:

```json
{
  "train": [
    {
      "path": "/path/to/dataset1/train",
      "text_column": "text",
      "split": "train",
      "name": "dataset1",
      "config": null
    },
    {
      "path": "/path/to/dataset2/train",
      "text_column": "paragraph",
      "split": "train",
      "name": "dataset2",
      "config": null
    }
  ],
  "test": [
    {
      "path": "/path/to/dataset1/test",
      "text_column": "text",
      "split": "test",
      "name": "dataset1",
      "config": null
    }
  ]
}
```

### Fields Explanation

- `path`: Local path to the HuggingFace dataset
- `text_column`: Name of the column containing the text data
- `split`: Dataset split (train/test/validation)
- `name`: Unique identifier for the dataset (used as source)
- `config`: Dataset configuration (can be null)

## Output Structure

The concatenated dataset will have the following columns:

- `id`: Unique identifier in format `{source}_{index}`
- `text`: The text content from the original `text_column`
- `source`: The dataset name from the configuration

## Token Analysis Report

The tool generates comprehensive markdown reports for each split (`token_analysis_report_{split}.md`) containing:

- **Overall Statistics**: Total examples, tokens, characters
- **Per-Source Breakdown**: Detailed statistics for each dataset
- **Token Distribution**: Visual representation of token distribution
- **Summary**: Key insights about the dataset composition

### Sample Report Structure

```markdown
# Dataset Token Analysis Report

**Tokenizer Used:** `bert-base-multilingual-cased`

**Total Examples:** 1,234,567
**Total Tokens:** 45,678,901
**Total Characters:** 234,567,890
**Average Tokens per Example:** 37.02

## Per-Source Statistics

| Source | Examples | Tokens | Characters | Avg Tokens/Example | % of Total Tokens |
|--------|----------|--------|------------|-------------------|------------------|
| fw2_fas | 500,000 | 20,000,000 | 120,000,000 | 40.00 | 43.8% |
| divan | 300,000 | 15,000,000 | 90,000,000 | 50.00 | 32.8% |
| meli | 200,000 | 8,000,000 | 48,000,000 | 40.00 | 17.5% |

## Token Distribution

**fw2_fas:** 43.8% ████████████████████████
**divan:** 32.8% ████████████████
**meli:** 17.5% ████████
```

## Requirements

```bash
pip install datasets transformers
```

## Error Handling

The script includes comprehensive error handling for:

- Missing dataset files
- Invalid JSON configuration
- Tokenizer loading errors
- Memory issues with large datasets

## Performance Considerations

- **Parallel Processing**: Uses multiple workers for dataset processing and token analysis
- **Batch Processing**: Token analysis is performed in batches for memory efficiency
- **Streaming**: Large datasets are processed in chunks to avoid memory issues
- **Progress Tracking**: Progress bars show the status of long-running operations
- **CPU Optimization**: Automatically limits workers to available CPU cores

## Example Usage Script

See `example_usage.py` for a complete example of how to use the concatenation tool with the Persian corpus dataset.

```bash
# Run full workflow (concatenation + separate token analysis)
python example_usage.py --full-analysis

# Run debug test (concatenation only, no token analysis)
python example_usage.py --debug-test

# Interactive mode
python example_usage.py
```

The `--full-analysis` option will:
1. Run concatenation without token analysis
2. Then run the separate token analysis script for each split

## Troubleshooting

### Common Issues

1. **Memory Errors**: Reduce batch size, num_workers, or process splits separately
   - Use `--skip_analysis` to avoid memory-intensive token counting
2. **Path Errors**: Ensure all dataset paths in the JSON are absolute and exist
3. **Tokenizer Errors**: Verify tokenizer name or path is correct
4. **Permission Errors**: Ensure write permissions for the output directory
5. **Performance Issues**: Adjust num_workers based on your system's CPU cores and memory
   - Use `--debug --skip_analysis` for faster testing without token analysis
6. **Token Analysis Errors**: Use the separate `analyze_dataset_tokens.py` script
   - Check dataset structure, ensure 'text' and 'source' columns exist
   - Use `--debug` flag in the analysis script for detailed information
   - Ensure the dataset path points to a valid concatenated dataset directory

### Debug Mode

For debugging, you can modify the logging level in the script:

```python
logging.basicConfig(level=logging.DEBUG)
```